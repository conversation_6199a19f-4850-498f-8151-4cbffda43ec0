"""
Example usage of the reCAPTCHA solver.
"""
import argparse
import os
import time
import logging
from colorama import init, Fore, Style
from recaptcha_solver_enhanced import solver

# Initialize colorama
init(autoreset=True)

def main():
    """Main function to run the reCAPTCHA solver example."""
    parser = argparse.ArgumentParser(description="Run reCAPTCHA solver example.")
    parser.add_argument(
        '--url', 
        type=str, 
        default="https://www.google.com/recaptcha/api2/demo", 
        help='Target URL with reCAPTCHA'
    )
    parser.add_argument(
        '--browser', 
        type=str, 
        default='chrome', 
        choices=['chrome', 'firefox', 'edge'], 
        help='Browser to use'
    )
    parser.add_argument(
        '--proxy', 
        type=str, 
        default=None, 
        help='Proxy server (host:port)'
    )
    parser.add_argument(
        '--no-verbose', 
        action='store_true', 
        help='Disable verbose output'
    )
    parser.add_argument(
        '--chrome-binary-path',
        type=str,
        default=None,
        help='Path to Chrome binary (optional, will auto-detect if not provided)'
    )
    args = parser.parse_args()

    # Configure logging
    log_level = logging.WARNING if args.no_verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    logging.info(f"\n{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    logging.info(f"{Fore.CYAN}{'reCAPTCHA Solver Example':^60}{Style.RESET_ALL}")
    logging.info(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")

    # This is often needed to bypass SSL certificate errors
    os.environ['PYTHONHTTPSVERIFY'] = '0'

    logging.info(f"\n{Fore.YELLOW}Attempting to solve reCAPTCHA on:{Style.RESET_ALL} {args.url}\n")
    if os.environ.get('PYTHONHTTPSVERIFY') == '0':
        logging.info(f"{Fore.BLUE}Note:{Style.RESET_ALL} SSL certificate verification is disabled.\n")

    start_time = time.time()

    # Call the solver with the parsed arguments
    result = solver(
        url=args.url,
        proxy=args.proxy,
        cookies=None,
        browser=args.browser,
        chrome_binary_path=args.chrome_binary_path
    )

    end_time = time.time()
    total_time = end_time - start_time

    logging.info(f"\n{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    logging.info(f"{Fore.CYAN}{'reCAPTCHA Solving Result':^60}{Style.RESET_ALL}")
    logging.info(f"{Fore.CYAN}{'-'*60}{Style.RESET_ALL}")
    
    if result and result.get('success') and result.get('recaptcha_token'):
        logging.info(f"{Fore.GREEN}✓ Success!{Style.RESET_ALL} reCAPTCHA appears to be solved.")
        token = result['recaptcha_token']
        # Display token safely
        token_display = f"{token[:20]}...{token[-20:]}" if len(token) > 40 else token
        logging.info(f"{Fore.WHITE}Token:{Style.RESET_ALL} {token_display}")
        logging.info(f"{Fore.WHITE}Time taken:{Style.RESET_ALL} {result.get('time_taken', 0):.2f} seconds")
    elif result and result.get('success'):
        logging.warning(f"{Fore.YELLOW}⚠ Process completed, but reCAPTCHA token was NOT found.{Style.RESET_ALL}")
        logging.info(f"{Fore.WHITE}Time taken:{Style.RESET_ALL} {result.get('time_taken', 0):.2f} seconds")
    else:
        logging.error(f"{Fore.RED}✗ Failed{Style.RESET_ALL} to solve reCAPTCHA or an error occurred.")
        if result and 'error' in result:
            logging.error(f"{Fore.WHITE}Error:{Style.RESET_ALL} {result['error']}")
        logging.info(f"{Fore.WHITE}Total time:{Style.RESET_ALL} {total_time:.2f} seconds")

    logging.info(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    logging.info(f"{Fore.CYAN}{'Example finished':^60}{Style.RESET_ALL}\n")

if __name__ == "__main__":
    main()